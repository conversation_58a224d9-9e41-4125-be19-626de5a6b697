'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/lib/supabase';
import { Loader2, Mail, Send } from 'lucide-react';
import { useEffect, useState } from 'react';

interface SupportTicket {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
}

interface EmailReplyDialogProps {
  ticket: SupportTicket | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEmailSent?: () => void;
}

export function EmailReplyDialog({ 
  ticket, 
  open, 
  onOpenChange, 
  onEmailSent 
}: EmailReplyDialogProps) {
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);

  // Initialize subject when ticket changes
  useEffect(() => {
    if (ticket) {
      setSubject(`Re: ${ticket.subject}`);
      setMessage(`Dear ${ticket.name},\n\nThank you for contacting VHRS Support.\n\n\n\nBest regards,\nVHRS Support Team`);
    }
  }, [ticket]);

  const handleSendEmail = async () => {
    if (!ticket || !subject.trim() || !message.trim()) {
      return;
    }

    setSending(true);
    try {
      // Call the send_email Edge Function
      const { data, error } = await supabase.functions.invoke('send_email', {
        body: {
          to: ticket.email,
          subject: subject.trim(),
          message: message.trim(),
          ticketId: ticket.id,
          replyToTicket: true
        }
      });

      if (error) {
        throw error;
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to send email');
      }

      // Close dialog and reset form
      onOpenChange(false);
      setSubject('');
      setMessage('');
      
      // Notify parent component
      if (onEmailSent) {
        onEmailSent();
      }

      // Show success message (you might want to use a toast notification here)
      alert('Email sent successfully!');

    } catch (error) {
      console.error('Error sending email:', error);
      alert(`Failed to send email: ${error.message}`);
    } finally {
      setSending(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setSubject('');
    setMessage('');
  };

  if (!ticket) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Reply to Support Ticket
          </DialogTitle>
          <DialogDescription>
            Send an email reply to {ticket.name} ({ticket.email})
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Original Ticket Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-sm text-gray-700 mb-2">Original Ticket</h4>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Subject:</span> {ticket.subject}
              </div>
              <div>
                <span className="font-medium">From:</span> {ticket.name} ({ticket.email})
              </div>
              <div>
                <span className="font-medium">Date:</span> {new Date(ticket.created_at).toLocaleDateString()}
              </div>
              <div>
                <span className="font-medium">Message:</span>
                <div className="mt-1 p-2 bg-white rounded border text-gray-600 max-h-32 overflow-y-auto">
                  {ticket.message}
                </div>
              </div>
            </div>
          </div>

          {/* Reply Form */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="reply-subject">Subject</Label>
              <Input
                id="reply-subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Email subject"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="reply-message">Message</Label>
              <Textarea
                id="reply-message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your reply message here..."
                rows={12}
                className="mt-1 resize-none"
              />
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={sending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSendEmail}
            disabled={sending || !subject.trim() || !message.trim()}
            className="min-w-[120px]"
          >
            {sending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Email
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
