'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { EnhancedVehicleHistoryReport } from '@/components/vehicles/EnhancedVehicleHistoryReport';
import VehicleEditForm from '@/components/vehicles/VehicleEditForm';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Vehicle {
  id: string;
  vin: string;
  make: string;
  model: string;
  year: number;
  vehicle_type: string;
  added_by: string;
  created_at: string;
}

interface LicensePlate {
  id: string;
  vehicle_id: string;
  license_plate: string;
  registration_centre: string;
  start_date: string;
  end_date: string | null;
  created_at: string;
}

interface VehicleColor {
  id: string;
  vehicle_id: string;
  color: string;
  effective_date: string;
  created_at: string;
}

interface OwnershipRecord {
  id: string;
  vehicle_id: string;
  owner_name: string;
  owner_phone: string;
  start_date: string;
  end_date: string | null;
  is_current: boolean;
  proof_url: string | null;
}

interface MaintenanceRecord {
  id: string;
  vehicle_id: string;
  service_type: string;
  date: string;
  mileage: number | null;
  garage_name: string | null;
  receipt_url: string | null;
}

interface AccidentRecord {
  id: string;
  vehicle_id: string;
  date: string;
  severity: 'minor' | 'major' | 'totaled';
  photos: string[] | null;
  reported_by: string | null;
}

interface MileageRecord {
  id: string;
  vehicle_id: string;
  mileage: number;
  date: string;
  source: 'user' | 'garage' | 'inspection';
  is_suspicious: boolean;
  flagged_by: string | null;
}

interface VehicleHistory {
  vin: string;
  make: string;
  model: string;
  year: number;
  type: string;
  ownership: any[];
  maintenance: any[];
  accidents: any[];
  generated_at: string;
}

export default function VehicleDetailPage() {
  // Initialize state
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [licensePlates, setLicensePlates] = useState<LicensePlate[]>([]);
  const [vehicleColors, setVehicleColors] = useState<VehicleColor[]>([]);
  const [ownershipRecords, setOwnershipRecords] = useState<OwnershipRecord[]>([]);
  const [maintenanceRecords, setMaintenanceRecords] = useState<MaintenanceRecord[]>([]);
  const [accidentRecords, setAccidentRecords] = useState<AccidentRecord[]>([]);
  const [mileageRecords, setMileageRecords] = useState<MileageRecord[]>([]);
  const [vehicleHistory, setVehicleHistory] = useState<VehicleHistory | null>(null);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const { isAdmin, hasPermission } = usePermissions();

  // Use the Next.js useParams hook to get the ID from the route
  const params = useParams();
  const vehicleId = typeof params.id === 'string' ? params.id : '';

  useEffect(() => {
    if (vehicleId) {
      fetchVehicleData(vehicleId);
    }
  }, [vehicleId]);

  const fetchVehicleData = async (id: string) => {
    setLoading(true);
    try {
      // Fetch vehicle details
      const { data: vehicleData, error: vehicleError } = await supabase
        .from('vehicles')
        .select('*')
        .eq('id', id)
        .single();

      if (vehicleError) throw vehicleError;
      setVehicle(vehicleData);

      // Fetch license plates
      const { data: platesData, error: platesError } = await supabase
        .from('vehicle_license_plates')
        .select('*')
        .eq('vehicle_id', id)
        .order('start_date', { ascending: false });

      if (platesError) throw platesError;
      setLicensePlates(platesData || []);

      // Fetch vehicle colors
      const { data: colorsData, error: colorsError } = await supabase
        .from('vehicle_colors')
        .select('*')
        .eq('vehicle_id', id)
        .order('effective_date', { ascending: false });

      if (colorsError) throw colorsError;
      setVehicleColors(colorsData || []);

      // Fetch ownership records
      const { data: ownershipData, error: ownershipError } = await supabase
        .from('ownership_history')
        .select('*')
        .eq('vehicle_id', id)
        .order('start_date', { ascending: false });

      if (ownershipError) throw ownershipError;
      setOwnershipRecords(ownershipData || []);

      // Fetch maintenance records
      const { data: maintenanceData, error: maintenanceError } = await supabase
        .from('maintenance_records')
        .select('*')
        .eq('vehicle_id', id)
        .order('date', { ascending: false });

      if (maintenanceError) throw maintenanceError;
      setMaintenanceRecords(maintenanceData || []);

      // Fetch accident records
      const { data: accidentData, error: accidentError } = await supabase
        .from('accidents')
        .select('*')
        .eq('vehicle_id', id)
        .order('date', { ascending: false });

      if (accidentError) throw accidentError;
      setAccidentRecords(accidentData || []);

      // Fetch mileage records
      const { data: mileageData, error: mileageError } = await supabase
        .from('mileage_records')
        .select('*')
        .eq('vehicle_id', id)
        .order('date', { ascending: false });

      if (mileageError) throw mileageError;
      setMileageRecords(mileageData || []);

    } catch (error) {
      console.error('Error fetching vehicle data:', error);
      toast.error('Failed to load vehicle data');
    } finally {
      setLoading(false);
    }
  };

  const generateVehicleHistory = async () => {
    // Use the stored vehicleId instead of accessing params directly
    if (!vehicleId) return;

    setHistoryLoading(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/generate_vehicle_history`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({ vehicle_id: vehicleId }),
      });

      const data = await response.json();

      if (data.status === 'success') {
        setVehicleHistory(data.history);
        setActiveTab('history');
        toast.success('Vehicle history record generated successfully');
      } else {
        console.error('Error generating vehicle history:', data.error);
        toast.error('Failed to generate vehicle history record');
      }
    } catch (error) {
      console.error('Error calling generate_vehicle_history function:', error);
      toast.error('Failed to generate vehicle history record');
    } finally {
      setHistoryLoading(false);
    }
  };

  const handleEditComplete = () => {
    setIsEditDialogOpen(false);
    if (vehicleId) {
      fetchVehicleData(vehicleId);
    }
    toast.success('Vehicle information updated successfully');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full md:col-span-2" />
        </div>
      </div>
    );
  }

  if (!vehicle) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">Vehicle Not Found</h2>
        <p className="text-neutral-600 mb-4">The requested vehicle could not be found.</p>
        <Link href="/dashboard/vehicles">
          <Button>Back to Vehicles</Button>
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">
          {vehicle.make} {vehicle.model} ({vehicle.year})
        </h1>
        <div className="flex gap-2">
          <Link href="/dashboard/vehicles">
            <Button variant="outline">Back to Vehicles</Button>
          </Link>
          <Button
            onClick={generateVehicleHistory}
            variant="default"
            disabled={historyLoading}
          >
            {historyLoading ? 'Generating...' : 'Generate History Report'}
          </Button>
          {(isAdmin || hasPermission('vehicle:edit')) && (
            <Button
              onClick={() => setIsEditDialogOpen(true)}
              variant="default"
            >
              Edit Vehicle
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="details">Vehicle Details</TabsTrigger>
          <TabsTrigger value="plates">License Plates</TabsTrigger>
          <TabsTrigger value="ownership">Ownership</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="accidents">Accidents</TabsTrigger>
          <TabsTrigger value="mileage">Mileage</TabsTrigger>
          <TabsTrigger value="history">History Record</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Vehicle Information</CardTitle>
              <CardDescription>Basic details about this vehicle</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-neutral-500">Make</h3>
                  <p className="mt-1">{vehicle.make}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500">Model</h3>
                  <p className="mt-1">{vehicle.model}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500">Year</h3>
                  <p className="mt-1">{vehicle.year}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500">Vehicle Type</h3>
                  <p className="mt-1 capitalize">{vehicle.vehicle_type}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500">VIN</h3>
                  <p className="mt-1 font-mono">{vehicle.vin}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500">Current Color</h3>
                  <p className="mt-1">
                    {vehicleColors.length > 0 ? vehicleColors[0].color : 'Not specified'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500">Current License Plate</h3>
                  <p className="mt-1 font-mono">
                    {licensePlates.length > 0 ? licensePlates[0].license_plate : 'Not specified'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500">Registration Centre</h3>
                  <p className="mt-1">
                    {licensePlates.length > 0 ? licensePlates[0].registration_centre : 'Not specified'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="plates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>License Plate History</CardTitle>
              <CardDescription>All license plates associated with this vehicle</CardDescription>
            </CardHeader>
            <CardContent>
              {licensePlates.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plate Number</TableHead>
                      <TableHead>Registration Centre</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {licensePlates.map((plate) => (
                      <TableRow key={plate.id}>
                        <TableCell className="font-medium font-mono">{plate.license_plate}</TableCell>
                        <TableCell>{plate.registration_centre || 'Not specified'}</TableCell>
                        <TableCell>{formatDate(plate.start_date)}</TableCell>
                        <TableCell>{plate.end_date ? formatDate(plate.end_date) : 'Current'}</TableCell>
                        <TableCell>
                          {!plate.end_date ? (
                            <Badge variant="default">Active</Badge>
                          ) : (
                            <Badge variant="outline">Inactive</Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-4">
                  <p className="text-neutral-500">No license plates found for this vehicle.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ownership" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Ownership History</CardTitle>
              <CardDescription>Record of all owners of this vehicle</CardDescription>
            </CardHeader>
            <CardContent>
              {ownershipRecords.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Owner Name</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Documentation</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {ownershipRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell className="font-medium">{record.owner_name}</TableCell>
                        <TableCell>{record.owner_phone}</TableCell>
                        <TableCell>{formatDate(record.start_date)}</TableCell>
                        <TableCell>{record.end_date ? formatDate(record.end_date) : 'Current'}</TableCell>
                        <TableCell>
                          {record.is_current ? (
                            <Badge variant="default">Current</Badge>
                          ) : (
                            <Badge variant="outline">Previous</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {record.proof_url ? (
                            <a
                              href={record.proof_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              View Document
                            </a>
                          ) : (
                            'Not available'
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-4">
                  <p className="text-neutral-500">No ownership records found for this vehicle.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Records</CardTitle>
              <CardDescription>Service and maintenance history</CardDescription>
            </CardHeader>
            <CardContent>
              {maintenanceRecords.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Service Type</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Mileage</TableHead>
                      <TableHead>Garage</TableHead>
                      <TableHead>Receipt</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {maintenanceRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell className="font-medium">{record.service_type}</TableCell>
                        <TableCell>{formatDate(record.date)}</TableCell>
                        <TableCell>{record.mileage ? `${record.mileage} km` : 'Not recorded'}</TableCell>
                        <TableCell>{record.garage_name || 'Not specified'}</TableCell>
                        <TableCell>
                          {record.receipt_url ? (
                            <a
                              href={record.receipt_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              View Receipt
                            </a>
                          ) : (
                            'Not available'
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-4">
                  <p className="text-neutral-500">No maintenance records found for this vehicle.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="accidents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Accident History</CardTitle>
              <CardDescription>Record of reported accidents</CardDescription>
            </CardHeader>
            <CardContent>
              {accidentRecords.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Photos</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {accidentRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>{formatDate(record.date)}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              record.severity === 'minor' ? 'outline' :
                              record.severity === 'major' ? 'secondary' :
                              'destructive'
                            }
                          >
                            {record.severity.charAt(0).toUpperCase() + record.severity.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {record.photos && record.photos.length > 0 ? (
                            <div className="flex gap-2">
                              {record.photos.map((photo, index) => (
                                <a
                                  key={index}
                                  href={photo}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:underline"
                                >
                                  Photo {index + 1}
                                </a>
                              ))}
                            </div>
                          ) : (
                            'No photos'
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-4">
                  <p className="text-neutral-500">No accident records found for this vehicle.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mileage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Mileage Records</CardTitle>
              <CardDescription>Odometer readings over time</CardDescription>
            </CardHeader>
            <CardContent>
              {mileageRecords.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Mileage (km)</TableHead>
                      <TableHead>Source</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mileageRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>{formatDate(record.date)}</TableCell>
                        <TableCell className="font-medium">{record.mileage}</TableCell>
                        <TableCell className="capitalize">{record.source}</TableCell>
                        <TableCell>
                          {record.is_suspicious ? (
                            <Badge variant="destructive">Suspicious</Badge>
                          ) : (
                            <Badge variant="outline">Normal</Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-4">
                  <p className="text-neutral-500">No mileage records found for this vehicle.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          {vehicleHistory ? (
            <>
              <div className="flex justify-end mb-4">
                <Link href={`/dashboard/vehicles/${vehicleId}/print`}>
                  <Button variant="outline" className="flex items-center gap-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <polyline points="6 9 6 2 18 2 18 9" />
                      <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2" />
                      <rect width="12" height="8" x="6" y="14" />
                    </svg>
                    <span>Printable Version</span>
                  </Button>
                </Link>
              </div>
              <EnhancedVehicleHistoryReport
                vehicleId={vehicleId}
                history={vehicleHistory}
                isLoading={historyLoading}
              />
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-64 bg-white rounded-lg border p-6">
              <h3 className="text-lg font-medium text-neutral-800 mb-2">No History Report Generated</h3>
                <p className="text-neutral-600 mb-4 text-center">
                Click the <strong>Generate History Report</strong> button to create a comprehensive vehicle history report.
                </p>
              <Button onClick={generateVehicleHistory} disabled={historyLoading}>
                {historyLoading ? 'Generating...' : 'Generate History Report'}
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Vehicle</DialogTitle>
            <DialogDescription>
              Update the details for this vehicle. Fields marked with * are required.
            </DialogDescription>
          </DialogHeader>
          {vehicle && (
            <VehicleEditForm
              vehicle={vehicle}
              onComplete={handleEditComplete}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
