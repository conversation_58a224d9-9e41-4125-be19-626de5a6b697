# EMAIL_PROVIDER System Guide

## 🎯 **Overview**

The `EMAIL_PROVIDER` system allows you to switch between different email providers (Gmail SMTP and Resend) using a single environment variable, without changing any code. This provides maximum flexibility and easy migration between email services.

## 🔧 **How It Works**

### **Single Unified Function**
- **One Function**: `send_email` handles both providers
- **Provider Detection**: Reads `EMAIL_PROVIDER` environment variable
- **Dynamic Routing**: Routes to appropriate email implementation
- **Same Interface**: Frontend code never changes

### **Provider Switching**
```bash
# Use Gmail SMTP
EMAIL_PROVIDER=smtp

# Use Resend API  
EMAIL_PROVIDER=resend
```

## 📁 **Implementation Architecture**

### **Unified Email Function Structure**
```typescript
// Main function - routes based on EMAIL_PROVIDER
async function sendEmail(config: EmailConfig, emailData: EmailRequest) {
  if (config.provider === 'smtp') {
    return await sendEmailSMTP(config, emailData)
  } else {
    return await sendEmailResend(config, emailData)
  }
}

// Provider-specific implementations
async function sendEmailResend(config, emailData) { /* Resend API logic */ }
async function sendEmailSMTP(config, emailData) { /* Gmail SMTP logic */ }
```

### **Dynamic Configuration**
```typescript
// Configuration adapts to provider
const emailProvider = Deno.env.get('EMAIL_PROVIDER') || 'resend'
const emailConfig = {
  provider: emailProvider,
  // Common settings
  fromName: Deno.env.get('MAIL_FROM_NAME'),
  fromEmail: Deno.env.get('MAIL_FROM_EMAIL'),
  replyTo: Deno.env.get('MAIL_REPLY_TO')
}

// Provider-specific settings added dynamically
if (emailProvider === 'smtp') {
  emailConfig.host = Deno.env.get('MAIL_HOST')
  emailConfig.username = Deno.env.get('MAIL_USERNAME')
  // etc...
} else {
  emailConfig.apiKey = Deno.env.get('RESEND_API_KEY')
}
```

## 🚀 **Current Setup**

### **Active Configuration (Gmail SMTP)**
```bash
# Provider Selection
EMAIL_PROVIDER=smtp

# SMTP Configuration (Gmail) - Currently Active
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password_here
MAIL_SECURE=true

# Common Settings
MAIL_FROM_NAME=VHRS Support
MAIL_FROM_EMAIL=<EMAIL>
MAIL_REPLY_TO=<EMAIL>

# Resend Configuration (Ready for future use)
# RESEND_API_KEY=re_your_api_key_here
```

## 🔄 **Switching Providers**

### **Switch to Resend (When Ready)**
1. **Update EMAIL_PROVIDER**:
   ```bash
   EMAIL_PROVIDER=resend
   ```

2. **Uncomment Resend Settings**:
   ```bash
   RESEND_API_KEY=re_your_actual_api_key
   MAIL_FROM_EMAIL=<EMAIL>
   ```

3. **Comment SMTP Settings** (optional):
   ```bash
   # MAIL_HOST=smtp.gmail.com
   # MAIL_USERNAME=<EMAIL>
   # etc...
   ```

4. **Restart Services**:
   ```bash
   supabase functions serve send_email
   ```

### **Switch Back to Gmail SMTP**
1. **Update EMAIL_PROVIDER**:
   ```bash
   EMAIL_PROVIDER=smtp
   ```

2. **Ensure SMTP Settings Active**:
   ```bash
   MAIL_HOST=smtp.gmail.com
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your_app_password
   ```

## 🎯 **Benefits of This Approach**

### **✅ Zero Code Changes**
- **Frontend**: Never needs modification
- **Database**: Same logging structure
- **Features**: All features work with both providers
- **Migration**: Change one environment variable

### **✅ Easy Testing**
- **Development**: Use Gmail SMTP (free, no domain needed)
- **Staging**: Test with Resend using test domain
- **Production**: Switch to production Resend setup
- **Rollback**: Instant fallback to previous provider

### **✅ Feature Parity**
- **Threading**: Both providers support conversation threading
- **Rate Limiting**: Same limits applied regardless of provider
- **Security**: Same authentication and permission checks
- **Logging**: Identical database logging and tracking

### **✅ Cost Optimization**
- **Development**: Free Gmail SMTP
- **Low Volume**: Gmail SMTP (500 emails/day)
- **High Volume**: Resend (3,000 free, then paid)
- **Enterprise**: Easy migration to enterprise providers

## 🔧 **Configuration Reference**

### **EMAIL_PROVIDER Values**
| Value | Provider | Use Case |
|-------|----------|----------|
| `smtp` | Gmail SMTP | Development, testing, no domain |
| `resend` | Resend API | Production, custom domain, scaling |

### **SMTP Provider Settings**
```bash
EMAIL_PROVIDER=smtp
MAIL_HOST=smtp.gmail.com          # SMTP server
MAIL_PORT=587                     # SMTP port
MAIL_USERNAME=<EMAIL>      # Gmail address
MAIL_PASSWORD=app_password        # Gmail app password
MAIL_SECURE=true                  # Use STARTTLS
```

### **Resend Provider Settings**
```bash
EMAIL_PROVIDER=resend
RESEND_API_KEY=re_abc123...       # Resend API key
```

### **Common Settings (Both Providers)**
```bash
MAIL_FROM_NAME=VHRS Support       # Display name
MAIL_FROM_EMAIL=<EMAIL>    # From address
MAIL_REPLY_TO=<EMAIL>   # Reply-to address
```

## 🧪 **Testing Different Providers**

### **Test Gmail SMTP**
```bash
# Set provider
EMAIL_PROVIDER=smtp

# Ensure Gmail settings are active
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password

# Test
curl -X POST http://localhost:54321/functions/v1/send_email \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{"to":"<EMAIL>","subject":"Test","message":"Hello"}'
```

### **Test Resend**
```bash
# Set provider
EMAIL_PROVIDER=resend

# Ensure Resend settings are active
RESEND_API_KEY=re_your_key

# Test (same API call)
curl -X POST http://localhost:54321/functions/v1/send_email \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{"to":"<EMAIL>","subject":"Test","message":"Hello"}'
```

## 📊 **Provider Comparison**

| Feature | Gmail SMTP | Resend |
|---------|------------|--------|
| **Setup Complexity** | Medium (app password) | Easy (API key) |
| **Domain Required** | No | Yes (production) |
| **Free Tier** | 500 emails/day | 3,000 emails/month |
| **Deliverability** | Good | Excellent |
| **Threading Support** | ✅ Full | ✅ Full |
| **Rate Limits** | 100/hour | API limits |
| **Professional Use** | Limited | Recommended |
| **Scaling** | Limited | Excellent |

## 🔍 **Troubleshooting**

### **Provider Not Switching**
- **Check**: `EMAIL_PROVIDER` value is correct
- **Restart**: Supabase functions after env changes
- **Verify**: Correct provider settings are uncommented

### **Configuration Errors**
```bash
# SMTP configuration incomplete
Error: Missing MAIL_USERNAME, MAIL_PASSWORD, or MAIL_FROM_EMAIL

# Resend configuration incomplete  
Error: Missing RESEND_API_KEY or MAIL_FROM_EMAIL
```

### **Debug Provider Selection**
Check function logs to see which provider is being used:
```bash
supabase functions logs send_email
# Look for: "SENDING EMAIL VIA GMAIL SMTP" or "SENDING EMAIL VIA RESEND"
```

## 🚀 **Production Deployment Strategy**

### **Phase 1: Development (Current)**
```bash
EMAIL_PROVIDER=smtp
# Use Gmail SMTP for development and testing
```

### **Phase 2: Staging**
```bash
EMAIL_PROVIDER=resend
# Test Resend with development domain
```

### **Phase 3: Production**
```bash
EMAIL_PROVIDER=resend
# Production Resend with custom domain
```

### **Phase 4: Scaling**
```bash
# Easy migration to enterprise providers
# Just add new provider to the switch statement
```

## 📋 **Migration Checklist**

### **Gmail SMTP → Resend**
- [ ] Get custom domain
- [ ] Set up Resend account
- [ ] Verify domain in Resend
- [ ] Get Resend API key
- [ ] Update `EMAIL_PROVIDER=resend`
- [ ] Update `RESEND_API_KEY`
- [ ] Update `MAIL_FROM_EMAIL` to custom domain
- [ ] Test thoroughly
- [ ] Monitor delivery rates

### **Resend → Gmail SMTP (Rollback)**
- [ ] Update `EMAIL_PROVIDER=smtp`
- [ ] Ensure Gmail app password is valid
- [ ] Test email sending
- [ ] Monitor for any issues

## 🎯 **Best Practices**

### **Environment Management**
- **Development**: Use Gmail SMTP
- **Staging**: Test target provider
- **Production**: Use production provider
- **Backup**: Keep both configurations ready

### **Monitoring**
- **Log Provider**: Check which provider is active
- **Track Delivery**: Monitor success rates
- **Error Handling**: Both providers have same error interface
- **Performance**: Compare response times

### **Security**
- **API Keys**: Never commit to version control
- **App Passwords**: Rotate regularly
- **Environment**: Use secure environment variable management
- **Access**: Limit who can change EMAIL_PROVIDER

## 🏆 **Summary**

The `EMAIL_PROVIDER` system gives you:
- ✅ **Flexibility**: Switch providers instantly
- ✅ **Zero Downtime**: No code changes required
- ✅ **Cost Optimization**: Use appropriate provider for each stage
- ✅ **Risk Mitigation**: Easy rollback if issues occur
- ✅ **Future-Proof**: Easy to add new providers

This architecture ensures your email system can evolve with your needs while maintaining reliability and ease of use.
