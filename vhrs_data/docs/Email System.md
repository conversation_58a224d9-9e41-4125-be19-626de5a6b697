# VHRS Email System Documentation

## Overview

The VHRS Email System provides in-house email functionality for replying to support tickets with advanced features including threaded conversations and automated status updates.

## Features

### ✅ **Threaded Email Conversations**
- Emails appear as replies in the recipient's email client
- Proper email threading using `Message-ID`, `In-Reply-To`, and `References` headers
- Maintains conversation context across multiple replies

### ✅ **Automated Status Updates**
- Automatically changes ticket status from `open` to `in_progress` when an agent replies
- Maintains audit trail of status changes

### ✅ **Permission-Based Access**
- Only users with `support_tickets:edit` permission can send email replies
- Secure JWT authentication and RLS policies

### ✅ **Email Reply Tracking**
- All sent emails are logged in the `support_ticket_replies` table
- Includes threading metadata for conversation continuity

## Database Schema

### Support Ticket Replies Table

```sql
CREATE TABLE support_ticket_replies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE NOT NULL,
  sender_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  recipient_email TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  message_id TEXT,     -- Email Message-ID for threading
  in_reply_to TEXT,    -- In-Reply-To header for threading
  references TEXT,     -- References header for threading
  sent_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Key Features:
- **Threading Support**: `message_id`, `in_reply_to`, and `email_references` fields enable proper email threading
- **Permission Inheritance**: Uses `support_tickets` permissions (excluded from auto-generation via separate migration)
- **Audit Trail**: Tracks who sent what and when

## API Functions

### 1. `send_support_ticket_reply(ticket_id, reply_subject, reply_message)`

**Purpose**: Prepares email reply with threading information and updates ticket status

**Features**:
- Generates unique Message-ID for email threading
- Sets up In-Reply-To and References headers for conversation continuity
- Auto-updates ticket status from `open` to `in_progress`
- Returns threading metadata for email sending

**Example Usage**:
```javascript
const { data } = await supabase.rpc('send_support_ticket_reply', {
  ticket_id: 'uuid-here',
  reply_subject: 'Re: Your Support Request',
  reply_message: 'Thank you for contacting us...'
});
```

**Response**:
```json
{
  "success": true,
  "ticket": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "subject": "Original Subject",
    "original_message": "Original message content",
    "status": "in_progress"
  },
  "reply": {
    "subject": "Re: Your Support Request",
    "message": "Thank you for contacting us..."
  },
  "threading": {
    "message_id": "<EMAIL>",
    "in_reply_to": "<EMAIL>",
    "references": "<EMAIL> <EMAIL>"
  }
}
```

### 2. `check_user_permission(user_id, permission_code)`

**Purpose**: Validates user permissions for email sending

**Example**:
```sql
SELECT check_user_permission('user-uuid', 'support_tickets:edit');
```

## Edge Function: `send_email`

### Location
- `vhrs_data/supabase/functions/send_email/index.ts`

### Features
- JWT authentication and permission validation
- Email threading header support
- Configurable email providers (currently simulated)
- Automatic logging of sent emails
- CORS support for frontend integration

### Request Format
```typescript
interface EmailRequest {
  to: string
  subject: string
  message: string
  ticketId?: string
  replyToTicket?: boolean
  threading?: {
    messageId: string
    inReplyTo: string
    references: string
  }
}
```

### Email Headers Generated
```
Message-ID: <EMAIL>
In-Reply-To: <EMAIL>
References: <EMAIL> <EMAIL>
```

## Frontend Integration

### Email Reply Dialog Component
- **Location**: `vhrs_web/src/components/ui/email-reply-dialog.tsx`
- **Features**:
  - Modal dialog for composing replies
  - Pre-populated templates
  - Integration with RPC and Edge functions
  - Form validation and loading states

### Usage Flow
1. User clicks "Reply via Email" on a support ticket
2. System calls `send_support_ticket_reply()` RPC function
3. RPC function generates threading info and updates ticket status
4. Frontend calls `send_email` Edge function with threading data
5. Edge function sends email and logs the reply
6. UI refreshes to show updated ticket status

## Email Threading Explained

### How Email Threading Works

Email threading relies on specific headers that email clients use to group related messages:

1. **Message-ID**: Unique identifier for each email
   - Format: `ticket-{uuid}-{timestamp}@vhrs.system`

2. **In-Reply-To**: References the Message-ID of the email being replied to
   - Links this email to its immediate parent

3. **References**: Contains all Message-IDs in the conversation thread
   - Maintains the complete conversation history

### Threading Logic

```
Original Ticket (conceptual):
Message-ID: <EMAIL>

First Reply:
Message-ID: <EMAIL>
In-Reply-To: <EMAIL>
References: <EMAIL>

Second Reply:
Message-ID: <EMAIL>
In-Reply-To: <EMAIL>
References: <EMAIL> <EMAIL>
```

## Configuration

### Environment Variables
```bash
# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM_NAME=VHRS Support
MAIL_FROM_EMAIL=<EMAIL>
```

### Supabase Configuration
```toml
[functions.send_email]
enabled = true
verify_jwt = true
import_map = "./functions/send_email/deno.json"
entrypoint = "./functions/send_email/index.ts"
```

## Production Email Integration

### Recommended Email Services

1. **SendGrid** (Recommended)
   - Excellent deliverability
   - Full threading support
   - Comprehensive APIs

2. **Mailgun**
   - Developer-friendly
   - Good threading support
   - Competitive pricing

3. **Amazon SES**
   - Cost-effective
   - Requires more setup
   - Good for high volume

### SendGrid Integration Example

```typescript
const emailPayload = {
  personalizations: [{
    to: [{ email: emailData.to }],
    subject: emailData.subject
  }],
  from: { 
    email: config.fromEmail, 
    name: config.fromName 
  },
  content: [{
    type: 'text/plain',
    value: emailData.message
  }],
  headers: {
    'Message-ID': emailData.threading.messageId,
    'In-Reply-To': emailData.threading.inReplyTo,
    'References': emailData.threading.references
  }
}

const response = await fetch('https://api.sendgrid.v3/mail/send', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${SENDGRID_API_KEY}`,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(emailPayload)
})
```

## Security Considerations

1. **Permission Validation**: All email operations require `support_tickets:edit` permission
2. **JWT Authentication**: Edge functions validate user tokens
3. **RLS Policies**: Database access controlled by Row Level Security
4. **Environment Variables**: Sensitive credentials stored securely
5. **Rate Limiting**: Consider implementing rate limits for production

## Testing

### Current Implementation
- Email sending is simulated with console logging
- All threading headers are generated and logged
- Database operations are fully functional

### Testing Steps
1. Login with admin account
2. Navigate to Support Tickets
3. Select a ticket and click "Reply via Email"
4. Fill out the form and send
5. Check console logs for threading headers
6. Verify ticket status changed to "in_progress"
7. Check `support_ticket_replies` table for logged email

## Future Enhancements

1. **Email Templates**: Rich HTML templates for professional emails
2. **Attachment Support**: File attachments in email replies
3. **Email Signatures**: Configurable email signatures
4. **Read Receipts**: Track when emails are opened
5. **Auto-responses**: Automated acknowledgment emails
6. **Email Scheduling**: Schedule emails for later sending
