# Gmail SMTP Setup Guide

## Overview

This guide shows you how to use Gmail SMTP for sending emails from your VHRS application. This is perfect for development and testing when you don't have a custom domain yet.

## 🚀 Quick Setup Steps

### 1. Enable 2-Factor Authentication on Gmail
1. Go to [myaccount.google.com](https://myaccount.google.com)
2. Click **Security** in the left sidebar
3. Under **Signing in to Google**, click **2-Step Verification**
4. Follow the setup process to enable 2FA

### 2. Generate App Password
1. Still in **Security** settings
2. Under **Signing in to Google**, click **App passwords**
3. Select **Mail** as the app
4. Select **Other (Custom name)** as the device
5. Enter "VHRS Support System" as the name
6. Click **Generate**
7. **Copy the 16-character password** (e.g., `abcd efgh ijkl mnop`)

### 3. Update Environment Variables

Your `vhrs_data/.env` is already configured for Gmail SMTP:

```bash
# SMTP Configuration (Gmail) - Currently Active
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=feoe bjtk pihe orgk  # Replace with your App Password
MAIL_SECURE=true
MAIL_FROM_NAME=VHRS Support
MAIL_FROM_EMAIL=<EMAIL>
MAIL_REPLY_TO=<EMAIL>
```

**Important**: Replace `feoe bjtk pihe orgk` with your actual App Password from step 2.

## 🔧 Configuration Details

### Environment Variables Explained

| Variable | Value | Description |
|----------|-------|-------------|
| `MAIL_HOST` | `smtp.gmail.com` | Gmail SMTP server |
| `MAIL_PORT` | `587` | SMTP port for STARTTLS |
| `MAIL_USERNAME` | Your Gmail address | Gmail account username |
| `MAIL_PASSWORD` | App Password | 16-character app password |
| `MAIL_SECURE` | `true` | Use STARTTLS encryption |
| `MAIL_FROM_NAME` | `VHRS Support` | Display name in emails |
| `MAIL_FROM_EMAIL` | Your Gmail address | From email address |
| `MAIL_REPLY_TO` | Your Gmail address | Reply-to address |

### Gmail SMTP Limits

- **Daily Limit**: 500 emails per day for free Gmail accounts
- **Rate Limit**: 100 emails per hour
- **Recipients**: Max 500 recipients per email
- **Size Limit**: 25MB per email (including attachments)

## 📧 Email Features

### ✅ **Threaded Conversations**
- Proper `Message-ID` generation
- `In-Reply-To` headers for replies
- `References` headers for conversation history
- Emails appear as conversation threads in email clients

### ✅ **Security Features**
- **App Passwords**: Secure authentication without exposing main password
- **STARTTLS**: Encrypted connection to Gmail servers
- **Rate Limiting**: 10 emails per minute per user in VHRS
- **Permission Checks**: Only authorized users can send emails

### ✅ **Professional Formatting**
- **RFC 2822 Compliant**: Proper email formatting
- **Custom Headers**: Threading and reply headers
- **MIME Support**: Proper content type headers
- **UTF-8 Encoding**: International character support

## 🧪 Testing the Setup

### 1. Apply Database Migrations
```bash
cd vhrs_data
supabase db reset
```

### 2. Start Services
```bash
# Start Supabase
supabase start

# Start Frontend (in another terminal)
cd ../vhrs_web
npm run dev
```

### 3. Test Email Sending
1. Login to your VHRS application
2. Go to Support Tickets
3. Select a ticket and click "Reply via Email"
4. Fill out the form and send
5. Check your Gmail **Sent** folder for the email
6. Check the recipient's inbox

### 4. Verify Threading
1. Send a reply to a support ticket
2. Reply to that email from your regular email client
3. Send another reply from VHRS
4. Check that emails appear as a conversation thread

## 🔍 Troubleshooting

### Common Issues

#### **"Authentication failed"**
- **Cause**: Incorrect app password or 2FA not enabled
- **Solution**: 
  1. Verify 2FA is enabled on your Google account
  2. Generate a new app password
  3. Update `MAIL_PASSWORD` in `.env`

#### **"SMTP connection failed"**
- **Cause**: Network issues or incorrect SMTP settings
- **Solution**:
  1. Check internet connection
  2. Verify `MAIL_HOST=smtp.gmail.com` and `MAIL_PORT=587`
  3. Ensure firewall allows outbound connections on port 587

#### **"Daily sending quota exceeded"**
- **Cause**: Sent more than 500 emails in 24 hours
- **Solution**: Wait for quota reset or upgrade to Google Workspace

#### **"Rate limit exceeded"**
- **Cause**: Sending too many emails too quickly
- **Solution**: Wait 1 minute and try again

### Debug Mode

To see detailed SMTP conversation logs:

```bash
# In vhrs_data directory
supabase functions serve send_email_smtp --debug
```

Check the console output for SMTP commands and responses.

## 📊 Monitoring

### Gmail Account Activity
- **Sent Mail**: Check Gmail sent folder
- **Account Activity**: [myaccount.google.com/device-activity](https://myaccount.google.com/device-activity)
- **Security**: Monitor for suspicious activity

### VHRS Database
Query sent emails:
```sql
SELECT 
  sr.*,
  st.subject as ticket_subject
FROM support_ticket_replies sr
JOIN support_tickets st ON sr.ticket_id = st.id
WHERE sr.external_id IS NOT NULL
ORDER BY sr.sent_at DESC;
```

## 🔄 Switching to Resend Later

When you're ready to use Resend with your domain:

### 1. Update Frontend
```typescript
// In email-reply-dialog.tsx, change:
const { data, error } = await supabase.functions.invoke('send_email_smtp', {
// Back to:
const { data, error } = await supabase.functions.invoke('send_email', {
```

### 2. Update Environment
```bash
# Comment out SMTP config
# MAIL_HOST=smtp.gmail.com
# MAIL_PORT=587
# etc...

# Uncomment Resend config
RESEND_API_KEY=your_actual_resend_key
MAIL_FROM_EMAIL=<EMAIL>
```

## 🚨 Security Considerations

### App Password Security
- **Never share** your app password
- **Rotate regularly** (generate new ones periodically)
- **Revoke unused** app passwords in Google settings
- **Monitor activity** in Google account security

### Email Content
- **No sensitive data** in email subjects or bodies
- **Validate input** to prevent injection attacks
- **Rate limiting** prevents spam and abuse
- **Audit trail** maintains compliance

## 📋 Production Considerations

### Gmail Limitations
- **500 emails/day**: May not be sufficient for high-volume applications
- **Consumer account**: Not ideal for business use
- **Deliverability**: May have lower reputation than dedicated email services

### Recommended Upgrade Path
1. **Start**: Gmail SMTP for development/testing
2. **Small Scale**: Google Workspace for business email
3. **Production**: Dedicated service like Resend, SendGrid, or Mailgun

### Business Email Options
- **Google Workspace**: $6/month per user, higher limits
- **Microsoft 365**: Similar pricing and features
- **Dedicated Services**: Resend, SendGrid, Mailgun for transactional emails

## ✅ Checklist

Before going live with Gmail SMTP:

- [ ] 2FA enabled on Gmail account
- [ ] App password generated and configured
- [ ] Test emails sent successfully
- [ ] Threading headers working correctly
- [ ] Rate limiting tested
- [ ] Database logging confirmed
- [ ] Error handling verified
- [ ] Security monitoring in place

## 🆘 Support

### Gmail Issues
- **Help Center**: [support.google.com/mail](https://support.google.com/mail)
- **App Passwords**: [support.google.com/accounts/answer/185833](https://support.google.com/accounts/answer/185833)
- **2FA Setup**: [support.google.com/accounts/answer/185839](https://support.google.com/accounts/answer/185839)

### VHRS Email System
- Check function logs: `supabase functions logs send_email_smtp`
- Database queries: Use Supabase Studio
- Frontend errors: Browser console

## 🎯 Next Steps

1. **Test thoroughly** with your Gmail account
2. **Monitor daily limits** to avoid quota issues
3. **Plan upgrade path** for production use
4. **Consider Google Workspace** for business features
5. **Prepare Resend migration** when domain is ready
