# Resend Email Service Setup Guide

## Overview

This guide walks you through setting up Resend for production-ready email sending in your VHRS application.

## 🚀 Quick Setup Steps

### 1. Create Resend Account
1. Go to [resend.com](https://resend.com)
2. Sign up for a free account (3,000 emails/month)
3. Verify your email address

### 2. Add Your Domain
1. In Resend dashboard, go to **Domains**
2. Click **Add Domain**
3. Enter your domain (e.g., `yourdomain.com`)
4. Add the required DNS records to your domain provider:
   - **MX Record**: `feedback-smtp.us-east-1.amazonses.com`
   - **TXT Record**: `v=spf1 include:amazonses.com ~all`
   - **CNAME Record**: `_amazonses.yourdomain.com`

### 3. Get API Key
1. Go to **API Keys** in Resend dashboard
2. Click **Create API Key**
3. Name it (e.g., "VHRS Production")
4. Copy the API key (starts with `re_`)

### 4. Update Environment Variables

Update your `vhrs_data/.env` file:

```bash
# Email Configuration for Support Ticket Replies (Resend)
RESEND_API_KEY=re_your_actual_api_key_here
MAIL_FROM_NAME=VHRS Support
MAIL_FROM_EMAIL=<EMAIL>
MAIL_REPLY_TO=<EMAIL>
```

**Important**: Replace `yourdomain.com` with your actual domain.

## 🔧 Configuration Options

### Environment Variables Explained

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `RESEND_API_KEY` | ✅ | Your Resend API key | `re_123abc...` |
| `MAIL_FROM_NAME` | ✅ | Display name for emails | `VHRS Support` |
| `MAIL_FROM_EMAIL` | ✅ | From email address | `<EMAIL>` |
| `MAIL_REPLY_TO` | ❌ | Reply-to address | `<EMAIL>` |

### Domain Requirements

- **Free Tier**: Can only send from `@resend.dev` addresses
- **Production**: Must verify your own domain
- **Subdomain**: Can use subdomains like `<EMAIL>`

## 📧 Email Features Implemented

### ✅ **Threaded Conversations**
- Proper `Message-ID` generation
- `In-Reply-To` headers for replies
- `References` headers for conversation history
- Emails appear as conversation threads in email clients

### ✅ **Rate Limiting**
- 10 emails per minute per user
- Prevents spam and abuse
- Configurable limits

### ✅ **Error Handling**
- Detailed error messages
- Resend API error reporting
- Graceful fallbacks

### ✅ **Audit Trail**
- All emails logged in database
- Resend email IDs stored for tracking
- Timestamps and sender information

## 🧪 Testing the Setup

### 1. Apply Database Migrations
```bash
cd vhrs_data
supabase db reset
```

### 2. Start Services
```bash
# Start Supabase
supabase start

# Start Frontend (in another terminal)
cd ../vhrs_web
npm run dev
```

### 3. Test Email Sending
1. Login to your VHRS application
2. Go to Support Tickets
3. Select a ticket and click "Reply via Email"
4. Fill out the form and send
5. Check the console logs for success/error messages

### 4. Verify in Resend Dashboard
1. Go to your Resend dashboard
2. Check **Logs** section
3. You should see your sent emails listed

## 🔍 Troubleshooting

### Common Issues

#### **"Email configuration is incomplete"**
- Check that `RESEND_API_KEY` and `MAIL_FROM_EMAIL` are set
- Verify API key format (should start with `re_`)

#### **"Domain not verified"**
- Complete domain verification in Resend dashboard
- Add all required DNS records
- Wait for DNS propagation (up to 24 hours)

#### **"Rate limit exceeded"**
- User is sending too many emails too quickly
- Wait 1 minute and try again
- Adjust rate limits in the Edge Function if needed

#### **"Resend API error: 403"**
- Invalid API key
- API key doesn't have permission for the domain
- Check API key in Resend dashboard

### Debug Mode

To enable detailed logging, check the Supabase function logs:

```bash
# In vhrs_data directory
supabase functions serve send_email --debug
```

## 📊 Monitoring & Analytics

### Resend Dashboard
- **Logs**: View all sent emails
- **Analytics**: Open rates, click rates
- **Bounces**: Failed deliveries
- **Complaints**: Spam reports

### Database Tracking
Query sent emails:
```sql
SELECT 
  sr.*,
  st.subject as ticket_subject,
  p.email as sender_email
FROM support_ticket_replies sr
JOIN support_tickets st ON sr.ticket_id = st.id
JOIN profiles p ON sr.sender_id = p.id
ORDER BY sr.sent_at DESC;
```

## 🚀 Production Considerations

### Security
- ✅ API keys stored in environment variables
- ✅ Rate limiting implemented
- ✅ Permission-based access control
- ✅ Input validation and sanitization

### Scalability
- **Free Tier**: 3,000 emails/month
- **Pro Tier**: $20/month for 50,000 emails
- **Enterprise**: Custom pricing for higher volumes

### Deliverability
- ✅ Proper threading headers
- ✅ SPF/DKIM records via domain verification
- ✅ Professional from addresses
- ✅ Reply-to addresses configured

### Backup Plan
If Resend is unavailable, consider:
1. **SendGrid**: Similar API, easy to swap
2. **Mailgun**: Alternative service
3. **Amazon SES**: Cost-effective for high volume

## 🔄 Switching Email Providers

The implementation is designed to be provider-agnostic. To switch to another service:

1. Update the `sendEmail` function in `send_email/index.ts`
2. Update environment variables
3. Test thoroughly

Example for SendGrid:
```typescript
// Replace Resend API call with SendGrid
const response = await fetch('https://api.sendgrid.v3/mail/send', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${config.apiKey}`,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(sendGridPayload)
})
```

## 📋 Checklist

Before going live:

- [ ] Domain verified in Resend
- [ ] DNS records added and propagated
- [ ] API key configured in environment
- [ ] Test emails sent successfully
- [ ] Rate limiting tested
- [ ] Error handling verified
- [ ] Database logging confirmed
- [ ] Threading headers working
- [ ] Production domain configured

## 🆘 Support

### Resend Support
- **Documentation**: [resend.com/docs](https://resend.com/docs)
- **Support**: Available in dashboard
- **Community**: Discord and GitHub

### VHRS Email System
- Check function logs: `supabase functions logs send_email`
- Database queries: Use Supabase Studio
- Frontend errors: Browser console

## 🎯 Next Steps

1. **Email Templates**: Create HTML templates for better formatting
2. **Attachments**: Add file attachment support
3. **Scheduling**: Implement delayed email sending
4. **Analytics**: Track open rates and engagement
5. **Automation**: Set up auto-responses and workflows
