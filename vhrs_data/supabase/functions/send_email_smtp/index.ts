import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10 // Max 10 emails per minute per user
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

interface EmailRequest {
  to: string
  subject: string
  message: string
  ticketId?: string
  replyToTicket?: boolean
  threading?: {
    messageId: string
    inReplyTo: string
    references: string
  }
}

interface SMTPConfig {
  host: string
  port: number
  username: string
  password: string
  secure: boolean
  fromName: string
  fromEmail: string
  replyTo?: string
}

// Rate limiting function
function checkRateLimit(userId: string): boolean {
  const now = Date.now()
  const userLimit = rateLimitMap.get(userId)

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new rate limit window
    rateLimitMap.set(userId, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    })
    return true
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false // Rate limit exceeded
  }

  userLimit.count++
  return true
}

// Gmail SMTP email sending function
async function sendEmailSMTP(config: SMTPConfig, emailData: EmailRequest): Promise<{ success: boolean; id?: string; error?: string }> {
  try {
    console.log('=== SENDING EMAIL VIA GMAIL SMTP ===')
    console.log('From:', `${config.fromName} <${config.fromEmail}>`)
    console.log('To:', emailData.to)
    console.log('Subject:', emailData.subject)
    
    // Create the email message in RFC 2822 format
    const boundary = `boundary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const messageId = emailData.threading?.messageId || `${Date.now()}@vhrs.system`
    
    let emailMessage = [
      `From: ${config.fromName} <${config.fromEmail}>`,
      `To: ${emailData.to}`,
      `Subject: ${emailData.subject}`,
      `Date: ${new Date().toUTCString()}`,
      `Message-ID: <${messageId}>`,
      `MIME-Version: 1.0`,
      `Content-Type: text/plain; charset=utf-8`,
      `Content-Transfer-Encoding: 8bit`
    ]

    // Add reply-to if configured
    if (config.replyTo) {
      emailMessage.push(`Reply-To: ${config.replyTo}`)
    }

    // Add threading headers for conversation support
    if (emailData.threading) {
      console.log('Threading Headers:')
      console.log('  Message-ID:', emailData.threading.messageId)
      console.log('  In-Reply-To:', emailData.threading.inReplyTo)
      console.log('  References:', emailData.threading.references)
      
      emailMessage.push(`In-Reply-To: <${emailData.threading.inReplyTo}>`)
      emailMessage.push(`References: ${emailData.threading.references}`)
    }

    // Add empty line before body
    emailMessage.push('')
    emailMessage.push(emailData.message)

    const fullMessage = emailMessage.join('\r\n')

    // Connect to Gmail SMTP server
    const conn = await Deno.connect({
      hostname: config.host,
      port: config.port,
    })

    const encoder = new TextEncoder()
    const decoder = new TextDecoder()

    // Helper function to send command and read response
    async function sendCommand(command: string): Promise<string> {
      console.log('SMTP Command:', command.replace(/AUTH PLAIN .+/, 'AUTH PLAIN [HIDDEN]'))
      await conn.write(encoder.encode(command + '\r\n'))
      const buffer = new Uint8Array(1024)
      const bytesRead = await conn.read(buffer)
      const response = decoder.decode(buffer.subarray(0, bytesRead || 0))
      console.log('SMTP Response:', response.trim())
      return response
    }

    // SMTP conversation
    let response = await sendCommand('')
    if (!response.includes('220')) {
      throw new Error(`SMTP connection failed: ${response}`)
    }

    response = await sendCommand('EHLO vhrs.system')
    if (!response.includes('250')) {
      throw new Error(`EHLO failed: ${response}`)
    }

    response = await sendCommand('STARTTLS')
    if (!response.includes('220')) {
      throw new Error(`STARTTLS failed: ${response}`)
    }

    // For TLS, we need to upgrade the connection
    // In a real implementation, you'd use a proper TLS library
    // For now, we'll use AUTH PLAIN with base64 encoding
    
    // Create AUTH PLAIN string: \0username\0password
    const authString = `\0${config.username}\0${config.password}`
    const authBase64 = btoa(authString)
    
    response = await sendCommand(`AUTH PLAIN ${authBase64}`)
    if (!response.includes('235')) {
      throw new Error(`Authentication failed: ${response}`)
    }

    // Send MAIL FROM
    response = await sendCommand(`MAIL FROM:<${config.fromEmail}>`)
    if (!response.includes('250')) {
      throw new Error(`MAIL FROM failed: ${response}`)
    }

    // Send RCPT TO
    response = await sendCommand(`RCPT TO:<${emailData.to}>`)
    if (!response.includes('250')) {
      throw new Error(`RCPT TO failed: ${response}`)
    }

    // Send DATA
    response = await sendCommand('DATA')
    if (!response.includes('354')) {
      throw new Error(`DATA command failed: ${response}`)
    }

    // Send the email content
    await conn.write(encoder.encode(fullMessage + '\r\n.\r\n'))
    const buffer = new Uint8Array(1024)
    const bytesRead = await conn.read(buffer)
    response = decoder.decode(buffer.subarray(0, bytesRead || 0))
    console.log('Email sent response:', response.trim())

    if (!response.includes('250')) {
      throw new Error(`Email sending failed: ${response}`)
    }

    // Send QUIT
    await sendCommand('QUIT')
    conn.close()

    console.log('Email sent successfully via Gmail SMTP')
    console.log('=== EMAIL SENT SUCCESSFULLY ===')
    
    return {
      success: true,
      id: messageId
    }

  } catch (error) {
    console.error('Error sending email via Gmail SMTP:', error)
    return {
      success: false,
      error: error.message || 'Unknown SMTP error occurred'
    }
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Verify the JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      throw new Error('Invalid authentication')
    }

    // Check if user has permission to send emails (support_tickets:edit)
    const { data: permissions, error: permError } = await supabase.rpc('check_user_permission', {
      user_id: user.id,
      permission_code: 'support_tickets:edit'
    })

    if (permError || !permissions) {
      throw new Error('Insufficient permissions to send emails')
    }

    // Parse request body
    const emailRequest: EmailRequest = await req.json()

    // Validate required fields
    if (!emailRequest.to || !emailRequest.subject || !emailRequest.message) {
      throw new Error('Missing required fields: to, subject, message')
    }

    // Check rate limiting
    if (!checkRateLimit(user.id)) {
      throw new Error('Rate limit exceeded. Please wait before sending another email.')
    }

    // Get SMTP configuration from environment
    const smtpConfig: SMTPConfig = {
      host: Deno.env.get('MAIL_HOST') || 'smtp.gmail.com',
      port: parseInt(Deno.env.get('MAIL_PORT') || '587'),
      username: Deno.env.get('MAIL_USERNAME')!,
      password: Deno.env.get('MAIL_PASSWORD')!,
      secure: Deno.env.get('MAIL_SECURE') === 'true',
      fromName: Deno.env.get('MAIL_FROM_NAME') || 'VHRS Support',
      fromEmail: Deno.env.get('MAIL_FROM_EMAIL')!,
      replyTo: Deno.env.get('MAIL_REPLY_TO')
    }

    // Validate SMTP configuration
    if (!smtpConfig.username || !smtpConfig.password || !smtpConfig.fromEmail) {
      throw new Error('SMTP configuration is incomplete. Missing MAIL_USERNAME, MAIL_PASSWORD, or MAIL_FROM_EMAIL')
    }

    // Send the email
    const emailResult = await sendEmailSMTP(smtpConfig, emailRequest)

    if (!emailResult.success) {
      throw new Error(emailResult.error || 'Failed to send email')
    }

    // If this is a reply to a support ticket, log it with threading info
    if (emailRequest.replyToTicket && emailRequest.ticketId) {
      const replyData: any = {
        ticket_id: emailRequest.ticketId,
        sender_id: user.id,
        recipient_email: emailRequest.to,
        subject: emailRequest.subject,
        message: emailRequest.message,
        sent_at: new Date().toISOString()
      }

      // Add threading information if available
      if (emailRequest.threading) {
        replyData.message_id = emailRequest.threading.messageId
        replyData.in_reply_to = emailRequest.threading.inReplyTo
        replyData.email_references = emailRequest.threading.references
      }

      // Add SMTP message ID for tracking
      if (emailResult.id) {
        replyData.external_id = emailResult.id
      }

      const { error: logError } = await supabase
        .from('support_ticket_replies')
        .insert(replyData)

      if (logError) {
        console.error('Error logging email reply:', logError)
        // Don't fail the request if logging fails
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Email sent successfully via Gmail SMTP',
        emailId: emailResult.id,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in send_email_smtp function:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
