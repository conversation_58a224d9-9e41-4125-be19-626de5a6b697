import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailRequest {
  to: string
  subject: string
  message: string
  ticketId?: string
  replyToTicket?: boolean
  threading?: {
    messageId: string
    inReplyTo: string
    references: string
  }
}

interface EmailConfig {
  host: string
  port: number
  username: string
  password: string
  fromName: string
  fromEmail: string
}

// Email sending function using a simpler approach with fetch to a third-party service
// For production, you might want to use SendGrid, Mailgun, or similar services
async function sendEmail(config: EmailConfig, emailData: EmailRequest): Promise<boolean> {
  try {
    // For now, we'll simulate email sending and log the details
    // In a real implementation, you would integrate with a proper email service
    console.log('=== EMAIL SENDING SIMULATION ===')
    console.log('From:', `${config.fromName} <${config.fromEmail}>`)
    console.log('To:', emailData.to)
    console.log('Subject:', emailData.subject)
    console.log('Message:', emailData.message)

    // Log threading headers for email conversation support
    if (emailData.threading) {
      console.log('Threading Headers:')
      console.log('  Message-ID:', emailData.threading.messageId)
      console.log('  In-Reply-To:', emailData.threading.inReplyTo)
      console.log('  References:', emailData.threading.references)
    }

    console.log('Config:', {
      host: config.host,
      port: config.port,
      username: config.username,
      // Don't log the password for security
    })
    console.log('=== END EMAIL SIMULATION ===')

    // For development/testing, we'll return true to simulate successful sending
    // In production, replace this with actual email sending logic

    // Example integration with a service like SendGrid with threading support:
    /*
    const emailPayload: any = {
      personalizations: [{
        to: [{ email: emailData.to }],
        subject: emailData.subject
      }],
      from: {
        email: config.fromEmail,
        name: config.fromName
      },
      content: [{
        type: 'text/plain',
        value: emailData.message
      }]
    }

    // Add threading headers for conversation support
    if (emailData.threading) {
      emailPayload.headers = {
        'Message-ID': emailData.threading.messageId,
        'In-Reply-To': emailData.threading.inReplyTo,
        'References': emailData.threading.references
      }
    }

    const response = await fetch('https://api.sendgrid.v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SENDGRID_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailPayload)
    })

    return response.ok
    */

    // For now, simulate a successful send
    return true
  } catch (error) {
    console.error('Error sending email:', error)
    return false
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Verify the JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      throw new Error('Invalid authentication')
    }

    // Check if user has permission to send emails (support_tickets:edit)
    const { data: permissions, error: permError } = await supabase.rpc('check_user_permission', {
      user_id: user.id,
      permission_code: 'support_tickets:edit'
    })

    if (permError || !permissions) {
      throw new Error('Insufficient permissions to send emails')
    }

    // Parse request body
    const emailRequest: EmailRequest = await req.json()

    // Validate required fields
    if (!emailRequest.to || !emailRequest.subject || !emailRequest.message) {
      throw new Error('Missing required fields: to, subject, message')
    }

    // Get email configuration from environment
    const emailConfig: EmailConfig = {
      host: Deno.env.get('MAIL_HOST') || 'smtp.gmail.com',
      port: parseInt(Deno.env.get('MAIL_PORT') || '587'),
      username: Deno.env.get('MAIL_USERNAME')!,
      password: Deno.env.get('MAIL_PASSWORD')!,
      fromName: Deno.env.get('MAIL_FROM_NAME') || 'VHRS Support',
      fromEmail: Deno.env.get('MAIL_FROM_EMAIL')!,
    }

    // Validate email configuration
    if (!emailConfig.username || !emailConfig.password || !emailConfig.fromEmail) {
      throw new Error('Email configuration is incomplete')
    }

    // Send the email
    const emailSent = await sendEmail(emailConfig, emailRequest)

    if (!emailSent) {
      throw new Error('Failed to send email')
    }

    // If this is a reply to a support ticket, log it with threading info
    if (emailRequest.replyToTicket && emailRequest.ticketId) {
      const replyData: any = {
        ticket_id: emailRequest.ticketId,
        sender_id: user.id,
        recipient_email: emailRequest.to,
        subject: emailRequest.subject,
        message: emailRequest.message,
        sent_at: new Date().toISOString()
      }

      // Add threading information if available
      if (emailRequest.threading) {
        replyData.message_id = emailRequest.threading.messageId
        replyData.in_reply_to = emailRequest.threading.inReplyTo
        replyData.email_references = emailRequest.threading.references
      }

      const { error: logError } = await supabase
        .from('support_ticket_replies')
        .insert(replyData)

      if (logError) {
        console.error('Error logging email reply:', logError)
        // Don't fail the request if logging fails
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Email sent successfully' 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in send_email function:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
