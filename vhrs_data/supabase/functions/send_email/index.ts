import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { sendEmailResend } from './providers/resend.ts'
import { sendEmailSMTP } from './providers/smtp.ts'
import type { EmailRequest, EmailResult, ResendConfig, SMTPConfig } from './types.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10 // Max 10 emails per minute per user
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

// Rate limiting function
function checkRateLimit(userId: string): boolean {
  const now = Date.now()
  const userLimit = rateLimitMap.get(userId)

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new rate limit window
    rateLimitMap.set(userId, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    })
    return true
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false // Rate limit exceeded
  }

  userLimit.count++
  return true
}

// Unified email sending function that routes to appropriate provider
async function sendEmail(provider: string, config: ResendConfig | SMTPConfig, emailData: EmailRequest): Promise<EmailResult> {
  if (provider === 'smtp') {
    return await sendEmailSMTP(config as SMTPConfig, emailData)
  } else {
    return await sendEmailResend(config as ResendConfig, emailData)
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Verify the JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      throw new Error('Invalid authentication')
    }

    // Check if user has permission to send emails (support_tickets:edit)
    const { data: permissions, error: permError } = await supabase.rpc('check_user_permission', {
      user_id: user.id,
      permission_code: 'support_tickets:edit'
    })

    if (permError || !permissions) {
      throw new Error('Insufficient permissions to send emails')
    }

    // Parse request body
    const emailRequest: EmailRequest = await req.json()

    // Validate required fields
    if (!emailRequest.to || !emailRequest.subject || !emailRequest.message) {
      throw new Error('Missing required fields: to, subject, message')
    }

    // Check rate limiting
    if (!checkRateLimit(user.id)) {
      throw new Error('Rate limit exceeded. Please wait before sending another email.')
    }

    // Get email provider from environment
    const emailProvider = Deno.env.get('EMAIL_PROVIDER') || 'resend'

    // Create provider-specific configuration
    let emailConfig: ResendConfig | SMTPConfig

    if (emailProvider === 'smtp') {
      emailConfig = {
        provider: 'smtp',
        host: Deno.env.get('MAIL_HOST') || 'smtp.gmail.com',
        port: parseInt(Deno.env.get('MAIL_PORT') || '587'),
        username: Deno.env.get('MAIL_USERNAME')!,
        password: Deno.env.get('MAIL_PASSWORD')!,
        secure: Deno.env.get('MAIL_SECURE') === 'true',
        fromName: Deno.env.get('MAIL_FROM_NAME') || 'VHRS Support',
        fromEmail: Deno.env.get('MAIL_FROM_EMAIL')!,
        replyTo: Deno.env.get('MAIL_REPLY_TO')
      }

      // Validate SMTP configuration
      if (!emailConfig.username || !emailConfig.password || !emailConfig.fromEmail) {
        throw new Error('SMTP configuration is incomplete. Missing MAIL_USERNAME, MAIL_PASSWORD, or MAIL_FROM_EMAIL')
      }
    } else {
      emailConfig = {
        provider: 'resend',
        apiKey: Deno.env.get('RESEND_API_KEY')!,
        fromName: Deno.env.get('MAIL_FROM_NAME') || 'VHRS Support',
        fromEmail: Deno.env.get('MAIL_FROM_EMAIL')!,
        replyTo: Deno.env.get('MAIL_REPLY_TO')
      }

      // Validate Resend configuration
      if (!emailConfig.apiKey || !emailConfig.fromEmail) {
        throw new Error('Resend configuration is incomplete. Missing RESEND_API_KEY or MAIL_FROM_EMAIL')
      }
    }

    // Send the email
    const emailResult = await sendEmail(emailProvider, emailConfig, emailRequest)

    if (!emailResult.success) {
      throw new Error(emailResult.error || 'Failed to send email')
    }

    // If this is a reply to a support ticket, log it with threading info
    if (emailRequest.replyToTicket && emailRequest.ticketId) {
      const replyData: any = {
        ticket_id: emailRequest.ticketId,
        sender_id: user.id,
        recipient_email: emailRequest.to,
        subject: emailRequest.subject,
        message: emailRequest.message,
        sent_at: new Date().toISOString()
      }

      // Add threading information if available
      if (emailRequest.threading) {
        replyData.message_id = emailRequest.threading.messageId
        replyData.in_reply_to = emailRequest.threading.inReplyTo
        replyData.email_references = emailRequest.threading.references
      }

      // Add Resend email ID for tracking
      if (emailResult.id) {
        replyData.external_id = emailResult.id
      }

      const { error: logError } = await supabase
        .from('support_ticket_replies')
        .insert(replyData)

      if (logError) {
        console.error('Error logging email reply:', logError)
        // Don't fail the request if logging fails
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Email sent successfully',
        emailId: emailResult.id,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in send_email function:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
