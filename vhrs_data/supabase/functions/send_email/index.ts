import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10 // Max 10 emails per minute per user
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

interface EmailRequest {
  to: string
  subject: string
  message: string
  ticketId?: string
  replyToTicket?: boolean
  threading?: {
    messageId: string
    inReplyTo: string
    references: string
  }
}

interface EmailConfig {
  apiKey: string
  fromName: string
  fromEmail: string
  replyTo?: string
}

interface ResendEmailPayload {
  from: string
  to: string[]
  subject: string
  text: string
  reply_to?: string
  headers?: Record<string, string>
}

interface ResendResponse {
  id: string
  from: string
  to: string[]
  created_at: string
}

// Rate limiting function
function checkRateLimit(userId: string): boolean {
  const now = Date.now()
  const userLimit = rateLimitMap.get(userId)

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new rate limit window
    rateLimitMap.set(userId, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    })
    return true
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false // Rate limit exceeded
  }

  userLimit.count++
  return true
}

// Email sending function using Resend API
async function sendEmail(config: EmailConfig, emailData: EmailRequest): Promise<{ success: boolean; id?: string; error?: string }> {
  try {
    console.log('=== SENDING EMAIL VIA RESEND ===')
    console.log('From:', `${config.fromName} <${config.fromEmail}>`)
    console.log('To:', emailData.to)
    console.log('Subject:', emailData.subject)

    // Prepare email payload for Resend
    const emailPayload: any = {
      from: `${config.fromName} <${config.fromEmail}>`,
      to: [emailData.to],
      subject: emailData.subject,
      text: emailData.message
    }

    // Add reply-to if configured
    if (config.replyTo) {
      emailPayload.reply_to = config.replyTo
    }

    // Add threading headers for conversation support
    if (emailData.threading) {
      console.log('Threading Headers:')
      console.log('  Message-ID:', emailData.threading.messageId)
      console.log('  In-Reply-To:', emailData.threading.inReplyTo)
      console.log('  References:', emailData.threading.references)

      emailPayload.headers = {
        'Message-ID': emailData.threading.messageId,
        'In-Reply-To': emailData.threading.inReplyTo,
        'References': emailData.threading.references
      }
    }

    // Send email via Resend API
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailPayload)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Resend API error:', response.status, errorText)
      return {
        success: false,
        error: `Resend API error: ${response.status} - ${errorText}`
      }
    }

    const result = await response.json()
    console.log('Email sent successfully via Resend:', result.id)
    console.log('=== EMAIL SENT SUCCESSFULLY ===')

    return {
      success: true,
      id: result.id
    }

  } catch (error) {
    console.error('Error sending email via Resend:', error)
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    }
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Verify the JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      throw new Error('Invalid authentication')
    }

    // Check if user has permission to send emails (support_tickets:edit)
    const { data: permissions, error: permError } = await supabase.rpc('check_user_permission', {
      user_id: user.id,
      permission_code: 'support_tickets:edit'
    })

    if (permError || !permissions) {
      throw new Error('Insufficient permissions to send emails')
    }

    // Parse request body
    const emailRequest: EmailRequest = await req.json()

    // Validate required fields
    if (!emailRequest.to || !emailRequest.subject || !emailRequest.message) {
      throw new Error('Missing required fields: to, subject, message')
    }

    // Check rate limiting
    if (!checkRateLimit(user.id)) {
      throw new Error('Rate limit exceeded. Please wait before sending another email.')
    }

    // Get email configuration from environment
    const emailConfig: EmailConfig = {
      apiKey: Deno.env.get('RESEND_API_KEY')!,
      fromName: Deno.env.get('MAIL_FROM_NAME') || 'VHRS Support',
      fromEmail: Deno.env.get('MAIL_FROM_EMAIL')!,
      replyTo: Deno.env.get('MAIL_REPLY_TO')
    }

    // Validate email configuration
    if (!emailConfig.apiKey || !emailConfig.fromEmail) {
      throw new Error('Email configuration is incomplete. Missing RESEND_API_KEY or MAIL_FROM_EMAIL')
    }

    // Send the email
    const emailResult = await sendEmail(emailConfig, emailRequest)

    if (!emailResult.success) {
      throw new Error(emailResult.error || 'Failed to send email')
    }

    // If this is a reply to a support ticket, log it with threading info
    if (emailRequest.replyToTicket && emailRequest.ticketId) {
      const replyData: any = {
        ticket_id: emailRequest.ticketId,
        sender_id: user.id,
        recipient_email: emailRequest.to,
        subject: emailRequest.subject,
        message: emailRequest.message,
        sent_at: new Date().toISOString()
      }

      // Add threading information if available
      if (emailRequest.threading) {
        replyData.message_id = emailRequest.threading.messageId
        replyData.in_reply_to = emailRequest.threading.inReplyTo
        replyData.email_references = emailRequest.threading.references
      }

      // Add Resend email ID for tracking
      if (emailResult.id) {
        replyData.external_id = emailResult.id
      }

      const { error: logError } = await supabase
        .from('support_ticket_replies')
        .insert(replyData)

      if (logError) {
        console.error('Error logging email reply:', logError)
        // Don't fail the request if logging fails
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Email sent successfully',
        emailId: emailResult.id,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in send_email function:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
