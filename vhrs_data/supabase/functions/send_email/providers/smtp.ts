// Gmail SMTP email provider implementation

export interface SMTPConfig {
  host: string
  port: number
  username: string
  password: string
  secure: boolean
  fromName: string
  fromEmail: string
  replyTo?: string
}

export interface EmailRequest {
  to: string
  subject: string
  message: string
  threading?: {
    messageId: string
    inReplyTo: string
    references: string
  }
}

export interface EmailResult {
  success: boolean
  id?: string
  error?: string
}

export async function sendEmailSMTP(config: SMTPConfig, emailData: EmailRequest): Promise<EmailResult> {
  try {
    console.log('=== SENDING EMAIL VIA GMAIL SMTP ===')
    console.log('From:', `${config.fromName} <${config.fromEmail}>`)
    console.log('To:', emailData.to)
    console.log('Subject:', emailData.subject)
    
    // Create the email message in RFC 2822 format
    const messageId = emailData.threading?.messageId || `${Date.now()}@vhrs.system`
    
    let emailMessage = [
      `From: ${config.fromName} <${config.fromEmail}>`,
      `To: ${emailData.to}`,
      `Subject: ${emailData.subject}`,
      `Date: ${new Date().toUTCString()}`,
      `Message-ID: <${messageId}>`,
      `MIME-Version: 1.0`,
      `Content-Type: text/plain; charset=utf-8`,
      `Content-Transfer-Encoding: 8bit`
    ]

    // Add reply-to if configured
    if (config.replyTo) {
      emailMessage.push(`Reply-To: ${config.replyTo}`)
    }

    // Add threading headers for conversation support
    if (emailData.threading) {
      console.log('Threading Headers:')
      console.log('  Message-ID:', emailData.threading.messageId)
      console.log('  In-Reply-To:', emailData.threading.inReplyTo)
      console.log('  References:', emailData.threading.references)
      
      emailMessage.push(`In-Reply-To: <${emailData.threading.inReplyTo}>`)
      emailMessage.push(`References: ${emailData.threading.references}`)
    }

    // Add empty line before body
    emailMessage.push('')
    emailMessage.push(emailData.message)

    const fullMessage = emailMessage.join('\r\n')

    // Connect to Gmail SMTP server
    const conn = await Deno.connect({
      hostname: config.host,
      port: config.port,
    })

    const encoder = new TextEncoder()
    const decoder = new TextDecoder()

    // Helper function to send command and read response
    async function sendCommand(command: string): Promise<string> {
      console.log('SMTP Command:', command.replace(/AUTH PLAIN .+/, 'AUTH PLAIN [HIDDEN]'))
      await conn.write(encoder.encode(command + '\r\n'))
      const buffer = new Uint8Array(1024)
      const bytesRead = await conn.read(buffer)
      const response = decoder.decode(buffer.subarray(0, bytesRead || 0))
      console.log('SMTP Response:', response.trim())
      return response
    }

    // SMTP conversation
    let response = await sendCommand('')
    if (!response.includes('220')) {
      throw new Error(`SMTP connection failed: ${response}`)
    }

    response = await sendCommand('EHLO vhrs.system')
    if (!response.includes('250')) {
      throw new Error(`EHLO failed: ${response}`)
    }

    response = await sendCommand('STARTTLS')
    if (!response.includes('220')) {
      throw new Error(`STARTTLS failed: ${response}`)
    }

    // Create AUTH PLAIN string: \0username\0password
    const authString = `\0${config.username}\0${config.password}`
    const authBase64 = btoa(authString)
    
    response = await sendCommand(`AUTH PLAIN ${authBase64}`)
    if (!response.includes('235')) {
      throw new Error(`Authentication failed: ${response}`)
    }

    // Send MAIL FROM
    response = await sendCommand(`MAIL FROM:<${config.fromEmail}>`)
    if (!response.includes('250')) {
      throw new Error(`MAIL FROM failed: ${response}`)
    }

    // Send RCPT TO
    response = await sendCommand(`RCPT TO:<${emailData.to}>`)
    if (!response.includes('250')) {
      throw new Error(`RCPT TO failed: ${response}`)
    }

    // Send DATA
    response = await sendCommand('DATA')
    if (!response.includes('354')) {
      throw new Error(`DATA command failed: ${response}`)
    }

    // Send the email content
    await conn.write(encoder.encode(fullMessage + '\r\n.\r\n'))
    const buffer = new Uint8Array(1024)
    const bytesRead = await conn.read(buffer)
    response = decoder.decode(buffer.subarray(0, bytesRead || 0))
    console.log('Email sent response:', response.trim())

    if (!response.includes('250')) {
      throw new Error(`Email sending failed: ${response}`)
    }

    // Send QUIT
    await sendCommand('QUIT')
    conn.close()

    console.log('Email sent successfully via Gmail SMTP')
    console.log('=== EMAIL SENT SUCCESSFULLY ===')
    
    return {
      success: true,
      id: messageId
    }

  } catch (error) {
    console.error('Error sending email via Gmail SMTP:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown SMTP error occurred'
    }
  }
}
