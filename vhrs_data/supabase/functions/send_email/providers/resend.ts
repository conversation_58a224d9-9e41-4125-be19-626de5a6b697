// Resend email provider implementation

export interface ResendConfig {
  apiKey: string
  fromName: string
  fromEmail: string
  replyTo?: string
}

export interface EmailRequest {
  to: string
  subject: string
  message: string
  threading?: {
    messageId: string
    inReplyTo: string
    references: string
  }
}

export interface EmailResult {
  success: boolean
  id?: string
  error?: string
}

export async function sendEmailResend(config: ResendConfig, emailData: EmailRequest): Promise<EmailResult> {
  try {
    console.log('=== SENDING EMAIL VIA RESEND ===')
    console.log('From:', `${config.fromName} <${config.fromEmail}>`)
    console.log('To:', emailData.to)
    console.log('Subject:', emailData.subject)
    
    // Prepare email payload for Resend
    const emailPayload: any = {
      from: `${config.fromName} <${config.fromEmail}>`,
      to: [emailData.to],
      subject: emailData.subject,
      text: emailData.message
    }

    // Add reply-to if configured
    if (config.replyTo) {
      emailPayload.reply_to = config.replyTo
    }

    // Add threading headers for conversation support
    if (emailData.threading) {
      console.log('Threading Headers:')
      console.log('  Message-ID:', emailData.threading.messageId)
      console.log('  In-Reply-To:', emailData.threading.inReplyTo)
      console.log('  References:', emailData.threading.references)
      
      emailPayload.headers = {
        'Message-ID': emailData.threading.messageId,
        'In-Reply-To': emailData.threading.inReplyTo,
        'References': emailData.threading.references
      }
    }

    // Send email via Resend API
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailPayload)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Resend API error:', response.status, errorText)
      return {
        success: false,
        error: `Resend API error: ${response.status} - ${errorText}`
      }
    }

    const result = await response.json()
    console.log('Email sent successfully via Resend:', result.id)
    console.log('=== EMAIL SENT SUCCESSFULLY ===')
    
    return {
      success: true,
      id: result.id
    }

  } catch (error) {
    console.error('Error sending email via Resend:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}
