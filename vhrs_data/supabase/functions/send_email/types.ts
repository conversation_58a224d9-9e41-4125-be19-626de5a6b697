// Shared types for email providers

export interface EmailRequest {
  to: string
  subject: string
  message: string
  ticketId?: string
  replyToTicket?: boolean
  threading?: {
    messageId: string
    inReplyTo: string
    references: string
  }
}

export interface EmailResult {
  success: boolean
  id?: string
  error?: string
}

export interface BaseEmailConfig {
  fromName: string
  fromEmail: string
  replyTo?: string
}

export interface ResendConfig extends BaseEmailConfig {
  provider: 'resend'
  apiKey: string
}

export interface SMTPConfig extends BaseEmailConfig {
  provider: 'smtp'
  host: string
  port: number
  username: string
  password: string
  secure: boolean
}

export type EmailConfig = ResendConfig | SMTPConfig
