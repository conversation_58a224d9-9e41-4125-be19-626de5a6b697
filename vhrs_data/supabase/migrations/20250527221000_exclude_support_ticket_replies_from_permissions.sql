-- Update the permission system to exclude support_ticket_replies table
-- This table uses support_tickets permissions instead of its own

-- Update the excluded tables list to include support_ticket_replies
CREATE OR REPLACE FUNCTION generate_permissions()
RETURNS TABLE(
  permission_code TEXT,
  permission_description TEXT,
  created BOOLEAN
) AS $$
DECLARE
  table_record RECORD;
  permission_exists BOOLEAN;
  permission_id UUID;
  action_record RECORD;
  actions TEXT[] := ARRAY['create', 'view', 'edit', 'delete'];
  action_descriptions TEXT[] := ARRAY['Add new', 'View', 'Modify', 'Delete'];
  excluded_tables TEXT[] := ARRAY[
    -- System tables
    'schema_migrations',
    'spatial_ref_sys',
    'pg_stat_statements',

    -- Tables managed through special permissions
    'permissions',
    'role_permissions',
    'user_roles',
    'profiles',
    'data_submissions',
    
    -- Tables that use parent table permissions
    'support_ticket_replies'  -- Uses support_tickets permissions
  ];
BEGIN
  -- Loop through all tables in the public schema
  FOR table_record IN
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE'
    AND table_name NOT IN (SELECT unnest(excluded_tables))
  LOOP
    -- For each table, create standard CRUD permissions
    FOR i IN 1..array_length(actions, 1) LOOP
      -- Format: table:action (e.g., vehicle:create)
      permission_code := table_record.table_name || ':' || actions[i];
      permission_description := action_descriptions[i] || ' ' || replace(table_record.table_name, '_', ' ') || ' (' || table_record.table_name || ')';

      -- Check if permission already exists
      SELECT EXISTS(
        SELECT 1 FROM permissions WHERE code = permission_code
      ) INTO permission_exists;

      IF NOT permission_exists THEN
        -- Insert new permission
        INSERT INTO permissions (code, description)
        VALUES (permission_code, permission_description)
        RETURNING id INTO permission_id;

        -- Return the created permission
        permission_code := permission_code;
        permission_description := permission_description;
        created := TRUE;
        RETURN NEXT;
      ELSE
        -- Return the existing permission
        permission_code := permission_code;
        permission_description := permission_description;
        created := FALSE;
        RETURN NEXT;
      END IF;
    END LOOP;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Update the table creation handler to exclude support_ticket_replies
CREATE OR REPLACE FUNCTION handle_table_creation()
RETURNS event_trigger AS $$
DECLARE
  obj RECORD;
  table_name TEXT;
  permission_code TEXT;
  permission_description TEXT;
  permission_exists BOOLEAN;
  permission_id UUID;
  actions TEXT[] := ARRAY['create', 'view', 'edit', 'delete'];
  action_descriptions TEXT[] := ARRAY['Add new', 'View', 'Modify', 'Delete'];
  excluded_tables TEXT[] := ARRAY[
    -- System tables
    'schema_migrations',
    'spatial_ref_sys',
    'pg_stat_statements',

    -- Tables managed through special permissions
    'permissions',
    'role_permissions',
    'user_roles',
    'profiles',
    'data_submissions',
    
    -- Tables that use parent table permissions
    'support_ticket_replies'  -- Uses support_tickets permissions
  ];
BEGIN
  -- Loop through created tables
  FOR obj IN SELECT * FROM pg_event_trigger_ddl_commands() WHERE command_tag = 'CREATE TABLE' LOOP
    -- Extract table name from the object identity
    table_name := regexp_replace(obj.object_identity, '.*\.', '');

    -- Skip excluded tables
    IF table_name = ANY(excluded_tables) THEN
      CONTINUE;
    END IF;

    -- For each table, create standard CRUD permissions
    FOR i IN 1..array_length(actions, 1) LOOP
      -- Format: table:action (e.g., vehicle:create)
      permission_code := table_name || ':' || actions[i];
      permission_description := action_descriptions[i] || ' ' || replace(table_name, '_', ' ') || ' (' || table_name || ')';

      -- Check if permission already exists
      SELECT EXISTS(
        SELECT 1 FROM permissions WHERE code = permission_code
      ) INTO permission_exists;

      IF NOT permission_exists THEN
        -- Insert new permission
        INSERT INTO permissions (code, description)
        VALUES (permission_code, permission_description)
        RETURNING id INTO permission_id;

        -- Log the creation
        RAISE NOTICE 'Created permission: %', permission_code;
      END IF;
    END LOOP;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Remove any existing permissions for support_ticket_replies if they were created
DELETE FROM role_permissions 
WHERE permission_id IN (
  SELECT id FROM permissions 
  WHERE code LIKE 'support_ticket_replies:%'
);

DELETE FROM permissions 
WHERE code LIKE 'support_ticket_replies:%';

-- Log the exclusion
DO $$
BEGIN
  RAISE NOTICE 'Excluded support_ticket_replies table from automatic permission generation';
  RAISE NOTICE 'This table uses support_tickets permissions for access control';
END $$;
