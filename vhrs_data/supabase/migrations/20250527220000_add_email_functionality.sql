-- Add support ticket replies table to track email communications
CREATE TABLE support_ticket_replies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE NOT NULL,
  sender_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  recipient_email TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  message_id TEXT, -- Email Message-ID for threading
  in_reply_to TEXT, -- In-Reply-To header for threading
  email_references TEXT, -- References header for threading (renamed to avoid reserved keyword)
  sent_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_support_ticket_replies_ticket_id ON support_ticket_replies(ticket_id);
CREATE INDEX idx_support_ticket_replies_sender_id ON support_ticket_replies(sender_id);
CREATE INDEX idx_support_ticket_replies_sent_at ON support_ticket_replies(sent_at DESC);

-- Enable RLS (Row Level Security)
ALTER TABLE support_ticket_replies ENABLE ROW LEVEL SECURITY;

-- Policy: Users with support_tickets:view permission can view all replies
CREATE POLICY support_ticket_replies_view_policy ON support_ticket_replies
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = auth.uid() AND p.code = 'support_tickets:view'
    )
  );

-- Policy: Users with support_tickets:edit permission can create replies
CREATE POLICY support_ticket_replies_create_policy ON support_ticket_replies
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = auth.uid() AND p.code = 'support_tickets:edit'
    )
  );

-- Create RPC function to check user permissions
CREATE OR REPLACE FUNCTION check_user_permission(user_id UUID, permission_code TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    JOIN user_roles ur ON rp.role_id = ur.role_id
    WHERE ur.user_id = check_user_permission.user_id 
    AND p.code = check_user_permission.permission_code
  );
END;
$$;

-- Create RPC function to send email reply with threading support
CREATE OR REPLACE FUNCTION send_support_ticket_reply(
  ticket_id UUID,
  reply_subject TEXT,
  reply_message TEXT
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  ticket_record support_tickets%ROWTYPE;
  user_has_permission BOOLEAN;
  last_reply_record support_ticket_replies%ROWTYPE;
  message_id TEXT;
  in_reply_to TEXT;
  email_references TEXT;
  result JSON;
BEGIN
  -- Check if user has permission
  SELECT check_user_permission(auth.uid(), 'support_tickets:edit') INTO user_has_permission;

  IF NOT user_has_permission THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Insufficient permissions'
    );
  END IF;

  -- Get ticket details
  SELECT * INTO ticket_record FROM support_tickets WHERE id = ticket_id;

  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Ticket not found'
    );
  END IF;

  -- Generate unique message ID for threading
  message_id := 'ticket-' || ticket_id || '-' || extract(epoch from now()) || '@vhrs.system';

  -- Get the last reply for threading
  SELECT * INTO last_reply_record
  FROM support_ticket_replies
  WHERE ticket_id = send_support_ticket_reply.ticket_id
  ORDER BY created_at DESC
  LIMIT 1;

  -- Set up email threading headers
  IF last_reply_record.id IS NOT NULL THEN
    -- This is a follow-up reply
    in_reply_to := last_reply_record.message_id;
    IF last_reply_record.email_references IS NOT NULL THEN
      email_references := last_reply_record.email_references || ' ' || last_reply_record.message_id;
    ELSE
      email_references := last_reply_record.message_id;
    END IF;
  ELSE
    -- This is the first reply to the ticket
    in_reply_to := 'ticket-' || ticket_id || '-<EMAIL>';
    email_references := 'ticket-' || ticket_id || '-<EMAIL>';
  END IF;

  -- Auto-update ticket status to 'in_progress' when agent replies
  IF ticket_record.status = 'open' THEN
    UPDATE support_tickets
    SET status = 'in_progress', updated_at = NOW()
    WHERE id = ticket_id;
  END IF;

  -- Return ticket details and threading info for email sending
  RETURN json_build_object(
    'success', true,
    'ticket', json_build_object(
      'id', ticket_record.id,
      'email', ticket_record.email,
      'name', ticket_record.name,
      'subject', ticket_record.subject,
      'original_message', ticket_record.message,
      'status', ticket_record.status
    ),
    'reply', json_build_object(
      'subject', reply_subject,
      'message', reply_message
    ),
    'threading', json_build_object(
      'message_id', message_id,
      'in_reply_to', in_reply_to,
      'references', email_references
    )
  );
END;
$$;

-- Update the excluded tables list to include support_ticket_replies
-- This table uses support_tickets permissions instead of its own
CREATE OR REPLACE FUNCTION generate_permissions()
RETURNS TABLE(
  permission_code TEXT,
  permission_description TEXT,
  created BOOLEAN
) AS $$
DECLARE
  table_record RECORD;
  permission_exists BOOLEAN;
  permission_id UUID;
  action_record RECORD;
  actions TEXT[] := ARRAY['create', 'view', 'edit', 'delete'];
  action_descriptions TEXT[] := ARRAY['Add new', 'View', 'Modify', 'Delete'];
  excluded_tables TEXT[] := ARRAY[
    -- System tables
    'schema_migrations',
    'spatial_ref_sys',
    'pg_stat_statements',

    -- Tables managed through special permissions
    'permissions',
    'role_permissions',
    'user_roles',
    'profiles',
    'data_submissions',

    -- Tables that use parent table permissions
    'support_ticket_replies'  -- Uses support_tickets permissions
  ];
BEGIN
  -- Loop through all tables in the public schema
  FOR table_record IN
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE'
    AND table_name NOT IN (SELECT unnest(excluded_tables))
  LOOP
    -- For each table, create standard CRUD permissions
    FOR i IN 1..array_length(actions, 1) LOOP
      -- Format: table:action (e.g., vehicle:create)
      permission_code := table_record.table_name || ':' || actions[i];
      permission_description := action_descriptions[i] || ' ' || replace(table_record.table_name, '_', ' ') || ' (' || table_record.table_name || ')';

      -- Check if permission already exists
      SELECT EXISTS(
        SELECT 1 FROM permissions WHERE code = permission_code
      ) INTO permission_exists;

      IF NOT permission_exists THEN
        -- Insert new permission
        INSERT INTO permissions (code, description)
        VALUES (permission_code, permission_description)
        RETURNING id INTO permission_id;

        -- Return the created permission
        permission_code := permission_code;
        permission_description := permission_description;
        created := TRUE;
        RETURN NEXT;
      ELSE
        -- Return the existing permission
        permission_code := permission_code;
        permission_description := permission_description;
        created := FALSE;
        RETURN NEXT;
      END IF;
    END LOOP;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Update the table creation handler to exclude support_ticket_replies
CREATE OR REPLACE FUNCTION handle_table_creation()
RETURNS event_trigger AS $$
DECLARE
  obj RECORD;
  table_name TEXT;
  permission_code TEXT;
  permission_description TEXT;
  permission_exists BOOLEAN;
  permission_id UUID;
  actions TEXT[] := ARRAY['create', 'view', 'edit', 'delete'];
  action_descriptions TEXT[] := ARRAY['Add new', 'View', 'Modify', 'Delete'];
  excluded_tables TEXT[] := ARRAY[
    -- System tables
    'schema_migrations',
    'spatial_ref_sys',
    'pg_stat_statements',

    -- Tables managed through special permissions
    'permissions',
    'role_permissions',
    'user_roles',
    'profiles',
    'data_submissions',

    -- Tables that use parent table permissions
    'support_ticket_replies'  -- Uses support_tickets permissions
  ];
BEGIN
  -- Loop through created tables
  FOR obj IN SELECT * FROM pg_event_trigger_ddl_commands() WHERE command_tag = 'CREATE TABLE' LOOP
    -- Extract table name from the object identity
    table_name := regexp_replace(obj.object_identity, '.*\.', '');

    -- Skip excluded tables
    IF table_name = ANY(excluded_tables) THEN
      CONTINUE;
    END IF;

    -- For each table, create standard CRUD permissions
    FOR i IN 1..array_length(actions, 1) LOOP
      -- Format: table:action (e.g., vehicle:create)
      permission_code := table_name || ':' || actions[i];
      permission_description := action_descriptions[i] || ' ' || replace(table_name, '_', ' ') || ' (' || table_name || ')';

      -- Check if permission already exists
      SELECT EXISTS(
        SELECT 1 FROM permissions WHERE code = permission_code
      ) INTO permission_exists;

      IF NOT permission_exists THEN
        -- Insert new permission
        INSERT INTO permissions (code, description)
        VALUES (permission_code, permission_description)
        RETURNING id INTO permission_id;

        -- Log the creation
        RAISE NOTICE 'Created permission: %', permission_code;
      END IF;
    END LOOP;
  END LOOP;
END;
$$ LANGUAGE plpgsql;
