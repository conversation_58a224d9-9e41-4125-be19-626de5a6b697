-- Add support ticket replies table to track email communications
CREATE TABLE support_ticket_replies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE NOT NULL,
  sender_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  recipient_email TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  message_id TEXT, -- Email Message-ID for threading
  in_reply_to TEXT, -- In-Reply-To header for threading
  email_references TEXT, -- References header for threading (renamed to avoid reserved keyword)
  sent_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_support_ticket_replies_ticket_id ON support_ticket_replies(ticket_id);
CREATE INDEX idx_support_ticket_replies_sender_id ON support_ticket_replies(sender_id);
CREATE INDEX idx_support_ticket_replies_sent_at ON support_ticket_replies(sent_at DESC);

-- Enable RLS (Row Level Security)
ALTER TABLE support_ticket_replies ENABLE ROW LEVEL SECURITY;

-- Policy: Users with support_tickets:view permission can view all replies
CREATE POLICY support_ticket_replies_view_policy ON support_ticket_replies
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = auth.uid() AND p.code = 'support_tickets:view'
    )
  );

-- Policy: Users with support_tickets:edit permission can create replies
CREATE POLICY support_ticket_replies_create_policy ON support_ticket_replies
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = auth.uid() AND p.code = 'support_tickets:edit'
    )
  );

-- Create RPC function to check user permissions
CREATE OR REPLACE FUNCTION check_user_permission(user_id UUID, permission_code TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    JOIN user_roles ur ON rp.role_id = ur.role_id
    WHERE ur.user_id = check_user_permission.user_id 
    AND p.code = check_user_permission.permission_code
  );
END;
$$;

-- Create RPC function to send email reply with threading support
CREATE OR REPLACE FUNCTION send_support_ticket_reply(
  ticket_id UUID,
  reply_subject TEXT,
  reply_message TEXT
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  ticket_record support_tickets%ROWTYPE;
  user_has_permission BOOLEAN;
  last_reply_record support_ticket_replies%ROWTYPE;
  message_id TEXT;
  in_reply_to TEXT;
  email_references TEXT;
  result JSON;
BEGIN
  -- Check if user has permission
  SELECT check_user_permission(auth.uid(), 'support_tickets:edit') INTO user_has_permission;

  IF NOT user_has_permission THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Insufficient permissions'
    );
  END IF;

  -- Get ticket details
  SELECT * INTO ticket_record FROM support_tickets WHERE id = ticket_id;

  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Ticket not found'
    );
  END IF;

  -- Generate unique message ID for threading
  message_id := 'ticket-' || ticket_id || '-' || extract(epoch from now()) || '@vhrs.system';

  -- Get the last reply for threading
  SELECT * INTO last_reply_record
  FROM support_ticket_replies
  WHERE ticket_id = send_support_ticket_reply.ticket_id
  ORDER BY created_at DESC
  LIMIT 1;

  -- Set up email threading headers
  IF last_reply_record.id IS NOT NULL THEN
    -- This is a follow-up reply
    in_reply_to := last_reply_record.message_id;
    IF last_reply_record.email_references IS NOT NULL THEN
      email_references := last_reply_record.email_references || ' ' || last_reply_record.message_id;
    ELSE
      email_references := last_reply_record.message_id;
    END IF;
  ELSE
    -- This is the first reply to the ticket
    in_reply_to := 'ticket-' || ticket_id || '-<EMAIL>';
    email_references := 'ticket-' || ticket_id || '-<EMAIL>';
  END IF;

  -- Auto-update ticket status to 'in_progress' when agent replies
  IF ticket_record.status = 'open' THEN
    UPDATE support_tickets
    SET status = 'in_progress', updated_at = NOW()
    WHERE id = ticket_id;
  END IF;

  -- Return ticket details and threading info for email sending
  RETURN json_build_object(
    'success', true,
    'ticket', json_build_object(
      'id', ticket_record.id,
      'email', ticket_record.email,
      'name', ticket_record.name,
      'subject', ticket_record.subject,
      'original_message', ticket_record.message,
      'status', ticket_record.status
    ),
    'reply', json_build_object(
      'subject', reply_subject,
      'message', reply_message
    ),
    'threading', json_build_object(
      'message_id', message_id,
      'in_reply_to', in_reply_to,
      'references', email_references
    )
  );
END;
$$;


